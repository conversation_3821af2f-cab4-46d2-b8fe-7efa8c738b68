
[23-Sep-2025 09:38:51 UTC] [2025-09-23 09:38:51] [INFO] Modules initialized successfully
[23-Sep-2025 09:38:52 UTC] [2025-09-23 09:38:52] [INFO] Test message
[23-Sep-2025 09:39:37 UTC] [2025-09-23 09:39:37] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:39:38 UTC] [2025-09-23 09:39:38] [INFO] Mo<PERSON><PERSON> initialized successfully
[23-Sep-2025 09:40:40 UTC] [2025-09-23 09:40:40] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:41:46 UTC] [2025-09-23 09:41:46] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:42:47 UTC] [2025-09-23 09:42:47] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:44:47 UTC] [2025-09-23 09:44:47] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:46:47 UTC] [2025-09-23 09:46:47] [INFO] Modules initialized successfully
[23-Sep-2025 09:48:41 UTC] [2025-09-23 09:48:41] [INFO] <PERSON><PERSON><PERSON> initialized successfully
[23-Sep-2025 09:48:42 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:48:42 UTC] [2025-09-23 09:48:42] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:48:43 UTC] [2025-09-23 09:48:43] [DEBUG] Debug: Retrieved pending image generation: 18 | Context: []
[23-Sep-2025 09:48:43 UTC] [2025-09-23 09:48:43] [DEBUG] Content Processing: ID 18 | Operation: image_prompt_generation | Result: Failed
[23-Sep-2025 09:48:47 UTC] [2025-09-23 09:48:47] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 09:48:47 UTC] [2025-09-23 09:48:47] [DEBUG] Debug: OpenAI Generated Content: **Image Prompt:**

Create a modern minimalist scene that embodies the theme "Melihat Lebih Dalam dari yang Terlihat." Use a dramatic perspective to capture a solitary figure in prayer, surrounded by soft morning light that filters through abstract shapes resembling spiritual barriers. Incorporate subtle purple accents (#6B46C1, #9333EA) in the background, hinting at the unseen forces at play. Light rays should illuminate a faint cross, symbolizing hope and strength. The atmosphere should evoke a sense of deep reflection and spiritual warfare, reminding viewers that the true battle lies beyond the visible. | Context: []
[23-Sep-2025 09:48:47 UTC] [2025-09-23 09:48:47] [INFO] Modules initialized successfully
[23-Sep-2025 09:49:31 UTC] [2025-09-23 09:49:31] [INFO] OpenAI Call: image | Model: gpt-image-1 | Tokens: N/A
[23-Sep-2025 09:49:32 UTC] [2025-09-23 09:49:32] [DEBUG] Database Operation: update on efm_efr_renungan_detail | Result: Success
[23-Sep-2025 09:49:32 UTC] [2025-09-23 09:49:32] [DEBUG] Content Processing: ID 18 | Operation: image_generation_completed | Result: Failed
[23-Sep-2025 09:49:32 UTC] [2025-09-23 09:49:32] [INFO] Image Generated: Prompt Length: 612 chars | Attachment ID: 149 | Generation Time: 44 seconds
[23-Sep-2025 09:49:32 UTC] [2025-09-23 09:49:32] [INFO] API Response: generate-image | Status: 200 | Response: {"success":true,"message":"Image generated successfully","id":"18","attachment_id":149,"image_prompt":"**Image Prompt:**\n\nCreate a modern minimalist scene that embodies the theme \"Melihat Lebih Dalam dari yang Terlihat.\" Use a dramatic perspective to capture a solitary figure in prayer, surrounded by soft morning light that filters through abstract shapes resembling spiritual barriers. Incorporate subtle purple accents (#6B46C1, #9333EA) in the background, hinting at the unseen forces at play. Light rays should illuminate a faint cross, symbolizing hope and strength. The atmosphere should evoke a sense of deep reflection and spiritual warfare, reminding viewers that the true battle lies beyond the visible."}
[23-Sep-2025 09:49:32 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:50:47 UTC] [2025-09-23 09:50:47] [INFO] Modules initialized successfully
[23-Sep-2025 09:51:34 UTC] [2025-09-23 09:51:34] [INFO] Modules initialized successfully
[23-Sep-2025 09:51:34 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:51:34 UTC] [2025-09-23 09:51:34] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:51:34 UTC] [2025-09-23 09:51:34] [DEBUG] Debug: Retrieved pending image generation: 17 | Context: []
[23-Sep-2025 09:51:34 UTC] [2025-09-23 09:51:34] [DEBUG] Content Processing: ID 17 | Operation: image_prompt_generation | Result: Failed
[23-Sep-2025 09:51:39 UTC] [2025-09-23 09:51:39] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 09:51:39 UTC] [2025-09-23 09:51:39] [DEBUG] Debug: OpenAI Generated Content: Create a photorealistic image that captures a serene landscape at golden hour, with soft morning light illuminating a wide view of rolling hills. Incorporate subtle cross shapes formed by the natural textures of the landscape, and let light rays break through the clouds, symbolizing divine presence. Use purple accent colors to enhance the spiritual atmosphere, blending them into the sky and the foreground. The composition should evoke a sense of peace and victory, inviting viewers to feel the power of faith without any text or words. | Context: []
[23-Sep-2025 09:52:03 UTC] [2025-09-23 09:52:03] [INFO] OpenAI Call: image | Model: gpt-image-1 | Tokens: N/A
[23-Sep-2025 09:52:03 UTC] [2025-09-23 09:52:03] [DEBUG] Database Operation: update on efm_efr_renungan_detail | Result: Success
[23-Sep-2025 09:52:03 UTC] [2025-09-23 09:52:03] [DEBUG] Content Processing: ID 17 | Operation: image_generation_completed | Result: Failed
[23-Sep-2025 09:52:03 UTC] [2025-09-23 09:52:03] [INFO] Image Generated: Prompt Length: 539 chars | Attachment ID: 150 | Generation Time: 23 seconds
[23-Sep-2025 09:52:03 UTC] [2025-09-23 09:52:03] [INFO] API Response: generate-image | Status: 200 | Response: {"success":true,"message":"Image generated successfully","id":"17","attachment_id":150,"image_prompt":"Create a photorealistic image that captures a serene landscape at golden hour, with soft morning light illuminating a wide view of rolling hills. Incorporate subtle cross shapes formed by the natural textures of the landscape, and let light rays break through the clouds, symbolizing divine presence. Use purple accent colors to enhance the spiritual atmosphere, blending them into the sky and the foreground. The composition should evoke a sense of peace and victory, inviting viewers to feel the power of faith without any text or words."}
[23-Sep-2025 09:52:03 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:52:48 UTC] [2025-09-23 09:52:48] [INFO] Modules initialized successfully
[23-Sep-2025 09:52:51 UTC] [2025-09-23 09:52:51] [INFO] Modules initialized successfully
[23-Sep-2025 09:52:51 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:52:51 UTC] [2025-09-23 09:52:51] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:52:51 UTC] [2025-09-23 09:52:51] [DEBUG] Debug: Retrieved pending image generation: 18 | Context: []
[23-Sep-2025 09:52:51 UTC] [2025-09-23 09:52:51] [DEBUG] Content Processing: ID 18 | Operation: image_prompt_generation | Result: Failed
[23-Sep-2025 09:52:53 UTC] [2025-09-23 09:52:53] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 09:52:53 UTC] [2025-09-23 09:52:53] [DEBUG] Debug: OpenAI Generated Content: Create a photorealistic image that captures a serene landscape at golden hour, featuring a dramatic perspective of a subtle cross emerging from a misty forest. Soft light rays filter through the trees, casting gentle shadows on the ground, where abstract shapes and natural textures intertwine. Use purple accent colors to enhance the spiritual atmosphere, creating a sense of depth and mystery. The composition should evoke a feeling of introspection and strength, inviting viewers to look beyond the surface and engage in their spiritual journey. | Context: []
[23-Sep-2025 09:53:16 UTC] [2025-09-23 09:53:16] [INFO] OpenAI Call: image | Model: gpt-image-1 | Tokens: N/A
[23-Sep-2025 09:53:16 UTC] [2025-09-23 09:53:16] [DEBUG] Database Operation: update on efm_efr_renungan_detail | Result: Success
[23-Sep-2025 09:53:16 UTC] [2025-09-23 09:53:16] [DEBUG] Content Processing: ID 18 | Operation: image_generation_completed | Result: Failed
[23-Sep-2025 09:53:16 UTC] [2025-09-23 09:53:16] [INFO] Image Generated: Prompt Length: 548 chars | Attachment ID: 151 | Generation Time: 22 seconds
[23-Sep-2025 09:53:16 UTC] [2025-09-23 09:53:16] [INFO] API Response: generate-image | Status: 200 | Response: {"success":true,"message":"Image generated successfully","id":"18","attachment_id":151,"image_prompt":"Create a photorealistic image that captures a serene landscape at golden hour, featuring a dramatic perspective of a subtle cross emerging from a misty forest. Soft light rays filter through the trees, casting gentle shadows on the ground, where abstract shapes and natural textures intertwine. Use purple accent colors to enhance the spiritual atmosphere, creating a sense of depth and mystery. The composition should evoke a feeling of introspection and strength, inviting viewers to look beyond the surface and engage in their spiritual journey."}
[23-Sep-2025 09:53:16 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:53:30 UTC] [2025-09-23 09:53:30] [INFO] Modules initialized successfully
[23-Sep-2025 09:53:30 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:53:30 UTC] [2025-09-23 09:53:30] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:53:30 UTC] [2025-09-23 09:53:30] [DEBUG] Debug: Retrieved pending image generation: none | Context: []
[23-Sep-2025 09:53:30 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Elshadaifm_Renungan_Logger::log_warning() in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php:169
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_image(Object(WP_REST_Request))
#1 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#2 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#3 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#10 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#11 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#12 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php on line 169
[23-Sep-2025 09:55:59 UTC] [2025-09-23 09:55:59] [INFO] Modules initialized successfully
[23-Sep-2025 09:55:59 UTC] API Key Debug - Received: test... | Stored: efm_0d030b...
[23-Sep-2025 09:55:59 UTC] API Key Debug - Received: test... | Stored: efm_0d030b...
[23-Sep-2025 09:56:08 UTC] [2025-09-23 09:56:08] [INFO] Modules initialized successfully
[23-Sep-2025 09:56:08 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:56:08 UTC] [2025-09-23 09:56:08] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:56:08 UTC] [2025-09-23 09:56:08] [DEBUG] Debug: Retrieved pending image generation: none | Context: []
[23-Sep-2025 09:56:08 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Elshadaifm_Renungan_Logger::log_warning() in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php:169
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_image(Object(WP_REST_Request))
#1 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#2 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#3 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#10 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#11 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#12 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php on line 169
[23-Sep-2025 09:56:49 UTC] [2025-09-23 09:56:49] [INFO] Modules initialized successfully
[23-Sep-2025 09:56:49 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:56:49 UTC] [2025-09-23 09:56:49] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:56:49 UTC] [2025-09-23 09:56:49] [DEBUG] Debug: Retrieved pending image generation: none | Context: []
[23-Sep-2025 09:56:49 UTC] [2025-09-23 09:56:49] [WARNING] No pending image generation found
[23-Sep-2025 09:56:49 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:58:39 UTC] [2025-09-23 09:58:39] [INFO] Modules initialized successfully
[23-Sep-2025 09:58:40 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:58:40 UTC] [2025-09-23 09:58:40] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 09:58:40 UTC] [2025-09-23 09:58:40] [DEBUG] Debug: Retrieved pending image generation: none | Context: []
[23-Sep-2025 09:58:40 UTC] [2025-09-23 09:58:40] [WARNING] No pending image generation found
[23-Sep-2025 09:58:40 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:58:48 UTC] [2025-09-23 09:58:48] [INFO] Modules initialized successfully
[23-Sep-2025 09:58:48 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 09:58:48 UTC] [2025-09-23 09:58:48] [DEBUG] Debug: Retrieved pending content generation: 19 | Context: []
[23-Sep-2025 09:58:59 UTC] [2025-09-23 09:58:59] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 09:58:59 UTC] [2025-09-23 09:58:59] [DEBUG] Debug: OpenAI Generated Content: Saudara yang terkasih, dalam perjalanan hidup ini, kita sering kali dihadapkan pada berbagai tantangan yang membuat kita merasa lelah dan putus asa. Namun, kita perlu mengingat bahwa kita tidak sendirian dalam peperangan ini. Dalam Kisah Para Rasul 4:31, tertulis, "Dan setelah mereka berdoa, goyanglah tempat mereka berkumpul, dan mereka semua dipenuhi dengan Roh Kudus dan memberitakan firman Allah dengan berani." Ayat ini mengingatkan kita akan kekuatan doa yang mampu mengubah keadaan.

Peperangan rohani yang kita hadapi bukanlah peperangan yang terlihat dengan mata fisik, melainkan sebuah perjuangan yang terjadi di dalam hati dan pikiran kita. Ketika kita merasa lelah, sering kali kita cenderung untuk mundur dan menyerah. Namun, kita harus ingat bahwa penyelamatan jiwa adalah peperangan yang harus dimenangkan melalui doa. **Ketahuilah, doa adalah kunci kemenangan!** 

Pertama, mari kita berdoa dengan berani. Saat Anda berdoa, percayalah bahwa Tuhan mendengar setiap kata yang Anda ucapkan. Doa bukan hanya sekadar ritual, tetapi sebuah komunikasi yang intim dengan Sang Pencipta. Ketika kita berdoa, kita membuka diri untuk menerima kekuatan dan bimbingan-Nya. Seperti seorang prajurit yang bersiap untuk bertempur, kita harus berani mengangkat suara kita kepada Tuhan, memohon pertolongan dan kekuatan-Nya.

Kedua, penting bagi kita untuk bergabung dalam komunitas. Dalam perjalanan iman, dukungan dari teman-teman seiman sangatlah berharga. Ketika kita berdoa bersama, kita saling menguatkan dan membangun satu sama lain. Iman kita adalah kuda-kuda rohani yang membuat kita tetap berdiri teguh di tengah badai kehidupan. Mari kita cari kesempatan untuk berkumpul, berbagi cerita, dan berdoa bersama. Dalam kebersamaan, kita akan merasakan kehadiran Tuhan yang nyata.

Saudara, mari kita berdoa dan melihat bagaimana Tuhan menggerakkan tangan-Nya dalam hidup kita. Jangan biarkan kelelahan mengalahkan semangat kita. Ingatlah, setiap doa yang kita panjatkan adalah langkah menuju kemenangan. Tuhan selalu siap mendengarkan dan menjawab, asalkan kita berani untuk berseru kepada-Nya. Semoga kita semua diberdayakan untuk terus berdoa dan berjuang dalam peperangan rohani ini. Amin. | Context: []
[23-Sep-2025 09:58:59 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Elshadaifm_Renungan_API_Endpoints::format_success_response() in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php:152
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_content(Object(WP_REST_Request))
#1 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#2 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#3 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#10 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#11 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#12 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php on line 152
[23-Sep-2025 10:00:41 UTC] [2025-09-23 10:00:41] [INFO] Modules initialized successfully
[23-Sep-2025 10:00:45 UTC] [2025-09-23 10:00:45] [INFO] Modules initialized successfully
[23-Sep-2025 10:01:12 UTC] [2025-09-23 10:01:12] [INFO] Modules initialized successfully
[23-Sep-2025 10:01:12 UTC] API Key Debug - Received: none | Stored: efm_0d030b...
[23-Sep-2025 10:01:12 UTC] API Key Debug - Received: none | Stored: efm_0d030b...
[23-Sep-2025 10:01:22 UTC] [2025-09-23 10:01:22] [INFO] Modules initialized successfully
[23-Sep-2025 10:01:39 UTC] [2025-09-23 10:01:39] [INFO] Modules initialized successfully
[23-Sep-2025 10:01:39 UTC] API Key Debug - Received: none | Stored: efm_0d030b...
[23-Sep-2025 10:01:39 UTC] API Key Debug - Received: none | Stored: efm_0d030b...
[23-Sep-2025 10:01:51 UTC] [2025-09-23 10:01:51] [INFO] Modules initialized successfully
[23-Sep-2025 10:01:51 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:01:51 UTC] [2025-09-23 10:01:51] [DEBUG] Debug: Retrieved pending content generation: 20 | Context: []
[23-Sep-2025 10:02:03 UTC] [2025-09-23 10:02:03] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:02:03 UTC] [2025-09-23 10:02:03] [DEBUG] Debug: OpenAI Generated Content: Judul: Bersatu dengan Tuhan untuk Kemenangan

Saudara yang terkasih, dalam perjalanan hidup ini, kita sering dihadapkan pada berbagai tantangan yang dapat membuat kita merasa goyah. Dalam Yohanes 15:5, Tuhan Yesus mengingatkan kita, “Akulah pokok anggur, kamu adalah ranting-rantingnya. Barangsiapa tinggal di dalam Aku dan Aku di dalam dia, ia berbuah banyak; sebab di luar Aku, kamu tidak dapat berbuat apa-apa.” Ayat ini bukan hanya sekadar pengingat, tetapi juga sebuah panggilan untuk kita semua agar senantiasa bersatu dengan-Nya.

Ketika kita berbicara tentang bersatu dengan Tuhan, kita berbicara tentang sebuah hubungan yang intim dan mendalam. Tinggallah dalam Kristus, artinya kita harus meluangkan waktu setiap hari untuk berdoa dan membaca firman-Nya. Seperti seorang petani yang merawat tanamannya, kita pun harus merawat hubungan kita dengan Tuhan. Tanpa perawatan yang baik, tanaman tidak akan tumbuh subur. Begitu juga, tanpa kedekatan dengan Tuhan, kita akan mudah goyah saat menghadapi berbagai peperangan dalam hidup.

Selanjutnya, kita juga perlu mengenakan perlindungan-Nya. Dalam Efesus 6:11, kita diajarkan untuk mengenakan perlengkapan senjata Allah agar dapat berdiri melawan tipu muslihat musuh. Ini adalah langkah penting dalam perjalanan iman kita. Ketika kita melengkapi diri dengan firman Tuhan dan kekuatan-Nya, kita akan lebih siap menghadapi setiap tantangan yang datang. Kekuatan kita tidak terletak pada diri kita sendiri, tetapi pada Tuhan yang selalu menyertai kita.

Saudara, kedewasaan rohani tidak datang dengan sendirinya. Ia membutuhkan usaha dan komitmen dari kita untuk terus berakar dalam Tuhan. Mari kita lihat bagaimana buah yang dihasilkan dalam hidup kita ketika kita bersatu dengan-Nya. Ketika kita berdoa, membaca firman, dan mengenakan perlindungan-Nya, kita akan melihat perubahan yang nyata dalam diri kita. Kita akan menjadi lebih kuat, lebih bijaksana, dan lebih mampu menghadapi setiap tantangan yang ada.

Di akhir renungan ini, ingatlah bahwa tanpa Tuhan, kita tidak dapat berbuat apa-apa. Mari kita berkomitmen untuk tetap bersatu dengan-Nya, agar kita dapat mengalami kemenangan dalam setiap aspek kehidupan kita. Tuhan memberkati kita semua. | Context: []
[23-Sep-2025 10:02:03 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method Elshadaifm_Renungan_API_Endpoints::format_success_response() in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php:152
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_content(Object(WP_REST_Request))
#1 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#2 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#3 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#10 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#11 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#12 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php on line 152
[23-Sep-2025 10:02:34 UTC] [2025-09-23 10:02:34] [INFO] Modules initialized successfully
[23-Sep-2025 10:02:58 UTC] [2025-09-23 10:02:58] [INFO] Modules initialized successfully
[23-Sep-2025 10:02:59 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:02:59 UTC] [2025-09-23 10:02:59] [DEBUG] Debug: Retrieved pending content generation: 21 | Context: []
[23-Sep-2025 10:03:06 UTC] [2025-09-23 10:03:06] [INFO] Modules initialized successfully
[23-Sep-2025 10:03:08 UTC] [2025-09-23 10:03:08] [INFO] Modules initialized successfully
[23-Sep-2025 10:03:11 UTC] [2025-09-23 10:03:11] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:03:11 UTC] [2025-09-23 10:03:11] [DEBUG] Debug: OpenAI Generated Content: Saudara yang terkasih, dalam hidup ini, kita sering kali dihadapkan pada berbagai tantangan dan tekanan yang dapat membuat kita merasa tertekan, terutama ketika kebohongan dan ketakutan mengintai di sekitar kita. Dalam Yakobus 4:7, kita diingatkan untuk "Tunduklah kepada Allah, dan lawanlah Iblis, maka ia akan lari dari padamu." Ayat ini bukan hanya sekadar kata-kata, tetapi merupakan panggilan untuk bertindak dengan keyakinan dan keberanian.

Ketika kita menghadapi kebohongan yang datang dari musuh, penting bagi kita untuk mengingat bahwa kita memiliki senjata yang sangat kuat: Kebenaran. Setiap kali Anda merasa tertekan oleh kebohongan, ingatlah untuk mengucapkan kebenaran yang terdapat dalam firman Tuhan. Misalnya, saat Anda merasa tidak berharga, ingatlah bahwa Allah menciptakan Anda dengan tujuan dan kasih-Nya yang besar. Ucapkan kebenaran ini dengan iman, dan lihatlah bagaimana kekuatan kebohongan itu mulai pudar.

Namun, tidak hanya mengucapkan kebenaran yang penting, tetapi juga tunduk kepada Allah. Ketika kita berserah sepenuhnya kepada-Nya, kita mengakui bahwa Dia adalah sumber kekuatan kita. Dalam momen-momen ketakutan, ingatlah bahwa Iblis tidak takut pada orang Kristen yang diam. Dia gemetar ketika kita berdiri teguh dalam iman dan berpegang pada janji-janji Tuhan. Dengan berserah kepada Allah, kita memberi izin bagi-Nya untuk bekerja dalam hidup kita dan mengusir segala bentuk ketakutan yang mengganggu.

Saudara, mari kita berani menghadapi setiap tantangan yang datang. Kita tidak sendirian; Tuhan selalu bersama kita. Ketika kita bersikap berani dan berdiri teguh dalam kebenaran-Nya, kita akan melihat bagaimana Tuhan meneguhkan kita. Jangan biarkan kebohongan dan ketakutan menguasai hidup Anda. Ingatlah, dengan mengucapkan kebenaran dan tunduk kepada Allah, kita dapat mengalahkan musuh yang berusaha menjatuhkan kita.

Akhir kata, marilah kita terus berpegang pada firman Tuhan dan berjuang melawan kebohongan dengan iman yang teguh. Tuhan telah memberikan kita segala yang kita butuhkan untuk berdiri teguh. Mari kita percaya dan lihat bagaimana Dia bekerja dalam hidup kita. | Context: []
[23-Sep-2025 10:03:11 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:03:23 UTC] [2025-09-23 10:03:23] [INFO] Modules initialized successfully
[23-Sep-2025 10:03:23 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:03:23 UTC] [2025-09-23 10:03:23] [INFO] API Request: POST submit | Params: {"api_key":"efm_0d030b41983ce6f3e1c1886b9c4d5296","content":"Test content for submission"}
[23-Sep-2025 10:03:24 UTC] [2025-09-23 10:03:24] [DEBUG] Database Operation: insert on efm_efr_renungan | Result: Success
[23-Sep-2025 10:03:24 UTC] [2025-09-23 10:03:24] [INFO] API Response: submit | Status: 200 | Response: {"success":true,"message":"Renungan saved successfully","id":11,"hash":"d42d9943e92079f3c4086d71cf11b9cdd1c6b9f0629685d9565537ec0c377253"}
[23-Sep-2025 10:03:24 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:03:33 UTC] [2025-09-23 10:03:33] [INFO] Modules initialized successfully
[23-Sep-2025 10:03:34 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:03:54 UTC] [2025-09-23 10:03:54] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:03:54 UTC] PHP Warning:  Array to string conversion in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 56
[23-Sep-2025 10:03:54 UTC] PHP Warning:  Array to string conversion in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 56
[23-Sep-2025 10:03:54 UTC] PHP Fatal error:  Uncaught TypeError: trim(): Argument #1 ($string) must be of type string, array given in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php:64
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php(64): trim(Array)
#1 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php(71): Elshadaifm_Renungan_Helper::clean_markdown(Array, 'json')
#2 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\openai-service.php(44): Elshadaifm_Renungan_Helper::parse_json_response(Array)
#3 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php(101): Elshadaifm_Renungan_OpenAI_Service->extract_content_data('Test content fo...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_details(Object(WP_REST_Request))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#10 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#11 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#12 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#13 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#14 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#15 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#16 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 64
[23-Sep-2025 10:04:10 UTC] [2025-09-23 10:04:10] [INFO] Modules initialized successfully
[23-Sep-2025 10:04:13 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:04:13 UTC] [2025-09-23 10:04:13] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 10:04:13 UTC] [2025-09-23 10:04:13] [DEBUG] Debug: Retrieved pending image generation: 19 | Context: []
[23-Sep-2025 10:04:13 UTC] [2025-09-23 10:04:13] [DEBUG] Content Processing: ID 19 | Operation: image_prompt_generation | Result: Failed
[23-Sep-2025 10:04:15 UTC] [2025-09-23 10:04:15] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:04:15 UTC] [2025-09-23 10:04:15] [DEBUG] Debug: OpenAI Generated Content: Create a photorealistic image that captures a serene and intimate moment of prayer in a modern minimalist setting. Use soft morning light to illuminate a small group of diverse individuals gathered in a circle, their heads bowed in prayer. Incorporate subtle cross shapes formed by light rays filtering through a window, casting gentle shadows on natural textures like wood and stone. Accent the scene with purple hues (#6B46C1, #9333EA) in the decor, creating a calming atmosphere that conveys spiritual strength and community support. Focus on the emotional connection and the power of prayer without any text. | Context: []
[23-Sep-2025 10:04:21 UTC] [2025-09-23 10:04:21] [INFO] Modules initialized successfully
[23-Sep-2025 10:04:23 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:04:23 UTC] [2025-09-23 10:04:23] [INFO] API Request: POST generate-image | Params: {"content_id":"pending"}
[23-Sep-2025 10:04:23 UTC] [2025-09-23 10:04:23] [DEBUG] Debug: Retrieved pending image generation: 19 | Context: []
[23-Sep-2025 10:04:23 UTC] [2025-09-23 10:04:23] [DEBUG] Content Processing: ID 19 | Operation: image_prompt_generation | Result: Failed
[23-Sep-2025 10:04:25 UTC] [2025-09-23 10:04:25] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:04:25 UTC] [2025-09-23 10:04:25] [DEBUG] Debug: OpenAI Generated Content: Create a photorealistic image that captures a serene moment of prayer in a modern minimalist setting. Use soft morning light to illuminate a small group of diverse individuals gathered in a circle, their heads bowed in prayer. Incorporate subtle cross shapes in the background and light rays breaking through a window, casting a warm glow. Add abstract shapes and natural textures to enhance the spiritual atmosphere. Use purple accent colors (#6B46C1, #9333EA) to create a calming yet powerful ambiance, emphasizing the theme of spiritual warfare through prayer without any text or words. | Context: []
[23-Sep-2025 10:05:03 UTC] [2025-09-23 10:05:03] [INFO] OpenAI Call: image | Model: gpt-image-1 | Tokens: N/A
[23-Sep-2025 10:05:04 UTC] [2025-09-23 10:05:04] [DEBUG] Database Operation: update on efm_efr_renungan_detail | Result: Success
[23-Sep-2025 10:05:04 UTC] [2025-09-23 10:05:04] [DEBUG] Content Processing: ID 19 | Operation: image_generation_completed | Result: Failed
[23-Sep-2025 10:05:04 UTC] [2025-09-23 10:05:04] [INFO] Image Generated: Prompt Length: 612 chars | Attachment ID: 152 | Generation Time: 49 seconds
[23-Sep-2025 10:05:04 UTC] [2025-09-23 10:05:04] [INFO] API Response: generate-image | Status: 200 | Response: {"success":true,"message":"Image generated successfully","id":"19","attachment_id":152,"image_prompt":"Create a photorealistic image that captures a serene and intimate moment of prayer in a modern minimalist setting. Use soft morning light to illuminate a small group of diverse individuals gathered in a circle, their heads bowed in prayer. Incorporate subtle cross shapes formed by light rays filtering through a window, casting gentle shadows on natural textures like wood and stone. Accent the scene with purple hues (#6B46C1, #9333EA) in the decor, creating a calming atmosphere that conveys spiritual strength and community support. Focus on the emotional connection and the power of prayer without any text."}
[23-Sep-2025 10:05:04 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:05:20 UTC] [2025-09-23 10:05:20] [INFO] OpenAI Call: image | Model: gpt-image-1 | Tokens: N/A
[23-Sep-2025 10:05:20 UTC] [2025-09-23 10:05:20] [DEBUG] Database Operation: update on efm_efr_renungan_detail | Result: Success
[23-Sep-2025 10:05:20 UTC] [2025-09-23 10:05:20] [DEBUG] Content Processing: ID 19 | Operation: image_generation_completed | Result: Failed
[23-Sep-2025 10:05:20 UTC] [2025-09-23 10:05:20] [INFO] Image Generated: Prompt Length: 589 chars | Attachment ID: 153 | Generation Time: 55 seconds
[23-Sep-2025 10:05:20 UTC] [2025-09-23 10:05:20] [INFO] API Response: generate-image | Status: 200 | Response: {"success":true,"message":"Image generated successfully","id":"19","attachment_id":153,"image_prompt":"Create a photorealistic image that captures a serene moment of prayer in a modern minimalist setting. Use soft morning light to illuminate a small group of diverse individuals gathered in a circle, their heads bowed in prayer. Incorporate subtle cross shapes in the background and light rays breaking through a window, casting a warm glow. Add abstract shapes and natural textures to enhance the spiritual atmosphere. Use purple accent colors (#6B46C1, #9333EA) to create a calming yet powerful ambiance, emphasizing the theme of spiritual warfare through prayer without any text or words."}
[23-Sep-2025 10:05:20 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:06:03 UTC] [2025-09-23 10:06:03] [INFO] Modules initialized successfully
[23-Sep-2025 10:06:03 UTC] WordPress database error Table 'elshadaifm2.efm_efr_renungan_content' doesn't exist for query SELECT COUNT(*) FROM efm_efr_renungan_content
[23-Sep-2025 10:06:03 UTC] WordPress database error Table 'elshadaifm2.efm_efr_renungan_details' doesn't exist for query SELECT COUNT(*) FROM efm_efr_renungan_details
[23-Sep-2025 10:06:03 UTC] WordPress database error Table 'elshadaifm2.efm_efr_renungan_details' doesn't exist for query SELECT id, ayat, judul FROM efm_efr_renungan_details WHERE content_generated = 0 LIMIT 5
[23-Sep-2025 10:06:03 UTC] WordPress database error Table 'elshadaifm2.efm_efr_renungan_details' doesn't exist for query SELECT id, ayat, judul FROM efm_efr_renungan_details WHERE image_generated = 0 AND content_generated = 1 LIMIT 5
[23-Sep-2025 10:06:03 UTC] WordPress database error Table 'elshadaifm2.efm_efr_renungan_details' doesn't exist for query SELECT id, ayat, judul, content_generated, image_generated FROM efm_efr_renungan_details WHERE content_generated = 1 AND image_generated = 1 LIMIT 5
[23-Sep-2025 10:06:24 UTC] [2025-09-23 10:06:24] [INFO] Modules initialized successfully
[23-Sep-2025 10:06:38 UTC] [2025-09-23 10:06:38] [INFO] Modules initialized successfully
[23-Sep-2025 10:46:54 UTC] [2025-09-23 10:46:54] [INFO] Modules initialized successfully
[23-Sep-2025 10:46:55 UTC] [2025-09-23 10:46:55] [INFO] Modules initialized successfully
[23-Sep-2025 10:46:56 UTC] API Key Debug - Received: invalid_ke... | Stored: efm_0d030b...
[23-Sep-2025 10:46:56 UTC] API Key Debug - Received: invalid_ke... | Stored: efm_0d030b...
[23-Sep-2025 10:47:32 UTC] [2025-09-23 10:47:32] [INFO] Modules initialized successfully
[23-Sep-2025 10:47:32 UTC] API Key Debug - Received: efm_0d030b... | Stored: efm_0d030b...
[23-Sep-2025 10:47:50 UTC] [2025-09-23 10:47:50] [INFO] OpenAI Call: chat | Model: gpt-4o-mini | Tokens: N/A
[23-Sep-2025 10:47:50 UTC] PHP Warning:  Array to string conversion in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 56
[23-Sep-2025 10:47:50 UTC] PHP Warning:  Array to string conversion in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 56
[23-Sep-2025 10:47:50 UTC] PHP Fatal error:  Uncaught TypeError: trim(): Argument #1 ($string) must be of type string, array given in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php:64
Stack trace:
#0 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php(64): trim(Array)
#1 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php(71): Elshadaifm_Renungan_Helper::clean_markdown(Array, 'json')
#2 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\openai-service.php(44): Elshadaifm_Renungan_Helper::parse_json_response(Array)
#3 D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\includes\api-endpoints.php(101): Elshadaifm_Renungan_OpenAI_Service->extract_content_data('Test content fo...')
#4 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1292): Elshadaifm_Renungan_API_Endpoints->handle_generate_details(Object(WP_REST_Request))
#5 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(1125): WP_REST_Server->respond_to_request(Object(WP_REST_Request), '/elshadaifm-ren...', Array, NULL)
#6 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api\class-wp-rest-server.php(439): WP_REST_Server->dispatch(Object(WP_REST_Request))
#7 D:\xampp82\htdocs\elshadaifm\wp-includes\rest-api.php(459): WP_REST_Server->serve_request('/elshadaifm-ren...')
#8 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(324): rest_api_loaded(Object(WP))
#9 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#10 D:\xampp82\htdocs\elshadaifm\wp-includes\plugin.php(565): WP_Hook->do_action(Array)
#11 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(418): do_action_ref_array('parse_request', Array)
#12 D:\xampp82\htdocs\elshadaifm\wp-includes\class-wp.php(818): WP->parse_request('')
#13 D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php(1342): WP->main('')
#14 D:\xampp82\htdocs\elshadaifm\wp-blog-header.php(16): wp()
#15 D:\xampp82\htdocs\elshadaifm\index.php(17): require('D:\\xampp82\\htdo...')
#16 {main}
  thrown in D:\xampp82\htdocs\elshadaifm\wp-content\plugins\elshadaifm-renungan-api\modules\helper.php on line 64
[23-Sep-2025 10:52:01 UTC] [2025-09-23 10:52:01] [INFO] Modules initialized successfully
[23-Sep-2025 10:52:09 UTC] [2025-09-23 10:52:09] [INFO] Modules initialized successfully
[23-Sep-2025 10:52:17 UTC] [2025-09-23 10:52:17] [INFO] Modules initialized successfully
[23-Sep-2025 10:52:22 UTC] [2025-09-23 10:52:22] [INFO] Modules initialized successfully
[23-Sep-2025 10:53:20 UTC] [2025-09-23 10:53:20] [INFO] Modules initialized successfully
[23-Sep-2025 10:55:20 UTC] [2025-09-23 10:55:20] [INFO] Modules initialized successfully
[23-Sep-2025 10:57:20 UTC] [2025-09-23 10:57:20] [INFO] Modules initialized successfully
[23-Sep-2025 10:59:20 UTC] [2025-09-23 10:59:20] [INFO] Modules initialized successfully
[23-Sep-2025 11:01:21 UTC] [2025-09-23 11:01:21] [INFO] Modules initialized successfully
[23-Sep-2025 11:03:21 UTC] [2025-09-23 11:03:21] [INFO] Modules initialized successfully
[23-Sep-2025 11:05:21 UTC] [2025-09-23 11:05:21] [INFO] Modules initialized successfully
[23-Sep-2025 11:07:21 UTC] [2025-09-23 11:07:21] [INFO] Modules initialized successfully
[23-Sep-2025 11:07:22 UTC] [2025-09-23 11:07:22] [INFO] Modules initialized successfully
[23-Sep-2025 11:09:22 UTC] [2025-09-23 11:09:22] [INFO] Modules initialized successfully
[23-Sep-2025 11:11:22 UTC] [2025-09-23 11:11:22] [INFO] Modules initialized successfully
[23-Sep-2025 11:13:22 UTC] [2025-09-23 11:13:22] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:22 UTC] [2025-09-23 11:15:22] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:45 UTC] [2025-09-23 11:15:45] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:46 UTC] [2025-09-23 11:15:46] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:56 UTC] [2025-09-23 11:15:56] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:56 UTC] [2025-09-23 11:15:56] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:57 UTC] PHP Deprecated:  File Theme without sidebar.php is <strong>deprecated</strong> since version 3.0.0 with no alternative available. Please include a sidebar.php template in your theme. in D:\xampp82\htdocs\elshadaifm\wp-includes\functions.php on line 6121
[23-Sep-2025 11:15:58 UTC] [2025-09-23 11:15:58] [INFO] Modules initialized successfully
[23-Sep-2025 11:15:59 UTC] [2025-09-23 11:15:59] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:01 UTC] [2025-09-23 11:16:01] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:06 UTC] [2025-09-23 11:16:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:07 UTC] [2025-09-23 11:16:07] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:08 UTC] [2025-09-23 11:16:08] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:13 UTC] [2025-09-23 11:16:13] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:15 UTC] [2025-09-23 11:16:15] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:19 UTC] [2025-09-23 11:16:19] [INFO] Modules initialized successfully
[23-Sep-2025 11:16:19 UTC] [2025-09-23 11:16:19] [INFO] Modules initialized successfully
[23-Sep-2025 11:17:32 UTC] [2025-09-23 11:17:32] [INFO] Modules initialized successfully
[23-Sep-2025 11:17:54 UTC] [2025-09-23 11:17:54] [INFO] Modules initialized successfully
[23-Sep-2025 11:19:48 UTC] [2025-09-23 11:19:48] [INFO] Modules initialized successfully
[23-Sep-2025 11:19:52 UTC] [2025-09-23 11:19:52] [INFO] Modules initialized successfully
[23-Sep-2025 11:19:53 UTC] [2025-09-23 11:19:53] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:00 UTC] [2025-09-23 11:20:00] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:33 UTC] [2025-09-23 11:20:33] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:44 UTC] [2025-09-23 11:20:44] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:45 UTC] [2025-09-23 11:20:45] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:47 UTC] [2025-09-23 11:20:47] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:48 UTC] [2025-09-23 11:20:48] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:51 UTC] [2025-09-23 11:20:51] [INFO] Modules initialized successfully
[23-Sep-2025 11:20:58 UTC] [2025-09-23 11:20:58] [INFO] Modules initialized successfully
[23-Sep-2025 11:21:08 UTC] [2025-09-23 11:21:08] [INFO] Modules initialized successfully
[23-Sep-2025 11:21:11 UTC] [2025-09-23 11:21:11] [INFO] Modules initialized successfully
[23-Sep-2025 11:21:41 UTC] [2025-09-23 11:21:41] [INFO] Modules initialized successfully
[23-Sep-2025 11:21:45 UTC] [2025-09-23 11:21:45] [INFO] Modules initialized successfully
[23-Sep-2025 11:23:08 UTC] [2025-09-23 11:23:08] [INFO] Modules initialized successfully
[23-Sep-2025 11:23:45 UTC] [2025-09-23 11:23:45] [INFO] Modules initialized successfully
[23-Sep-2025 11:23:51 UTC] [2025-09-23 11:23:51] [INFO] Modules initialized successfully
[23-Sep-2025 11:23:54 UTC] [2025-09-23 11:23:54] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:01 UTC] [2025-09-23 11:24:01] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:06 UTC] [2025-09-23 11:24:06] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:21 UTC] [2025-09-23 11:24:21] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:31 UTC] [2025-09-23 11:24:31] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:46 UTC] [2025-09-23 11:24:46] [INFO] Modules initialized successfully
[23-Sep-2025 11:24:59 UTC] [2025-09-23 11:24:59] [INFO] Modules initialized successfully
[23-Sep-2025 11:25:27 UTC] [2025-09-23 11:25:27] [INFO] Modules initialized successfully
[23-Sep-2025 11:25:38 UTC] [2025-09-23 11:25:38] [INFO] Modules initialized successfully
[23-Sep-2025 11:26:01 UTC] [2025-09-23 11:26:01] [INFO] Modules initialized successfully
[23-Sep-2025 11:26:46 UTC] [2025-09-23 11:26:46] [INFO] Modules initialized successfully
[23-Sep-2025 11:26:53 UTC] [2025-09-23 11:26:53] [INFO] Modules initialized successfully
[23-Sep-2025 11:26:58 UTC] [2025-09-23 11:26:58] [INFO] Modules initialized successfully
[23-Sep-2025 11:27:13 UTC] [2025-09-23 11:27:13] [INFO] Modules initialized successfully
[23-Sep-2025 11:28:01 UTC] [2025-09-23 11:28:01] [INFO] Modules initialized successfully
[23-Sep-2025 11:28:46 UTC] [2025-09-23 11:28:46] [INFO] Modules initialized successfully
[23-Sep-2025 11:28:49 UTC] [2025-09-23 11:28:49] [INFO] Modules initialized successfully
[23-Sep-2025 11:28:52 UTC] [2025-09-23 11:28:52] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:05 UTC] [2025-09-23 11:29:05] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:11 UTC] [2025-09-23 11:29:11] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:17 UTC] [2025-09-23 11:29:17] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:17 UTC] [2025-09-23 11:29:17] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:20 UTC] [2025-09-23 11:29:20] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:27 UTC] [2025-09-23 11:29:27] [INFO] Modules initialized successfully
[23-Sep-2025 11:29:36 UTC] [2025-09-23 11:29:36] [INFO] Modules initialized successfully
[23-Sep-2025 11:30:01 UTC] [2025-09-23 11:30:01] [INFO] Modules initialized successfully
[23-Sep-2025 11:30:46 UTC] [2025-09-23 11:30:46] [INFO] Modules initialized successfully
[23-Sep-2025 11:32:02 UTC] [2025-09-23 11:32:02] [INFO] Modules initialized successfully
[23-Sep-2025 11:32:35 UTC] [2025-09-23 11:32:35] [INFO] Modules initialized successfully
[23-Sep-2025 11:32:47 UTC] [2025-09-23 11:32:47] [INFO] Modules initialized successfully
[23-Sep-2025 11:32:55 UTC] [2025-09-23 11:32:55] [INFO] Modules initialized successfully
[23-Sep-2025 11:33:54 UTC] [2025-09-23 11:33:54] [INFO] Modules initialized successfully
[23-Sep-2025 11:34:20 UTC] [2025-09-23 11:34:20] [INFO] Modules initialized successfully
[23-Sep-2025 11:37:04 UTC] [2025-09-23 11:37:04] [INFO] Modules initialized successfully
[23-Sep-2025 11:37:09 UTC] [2025-09-23 11:37:09] [INFO] Modules initialized successfully
[23-Sep-2025 11:40:14 UTC] [2025-09-23 11:40:14] [INFO] Modules initialized successfully
[23-Sep-2025 11:54:08 UTC] [2025-09-23 11:54:08] [INFO] Modules initialized successfully
[23-Sep-2025 11:54:23 UTC] [2025-09-23 11:54:23] [INFO] Modules initialized successfully
[23-Sep-2025 11:55:40 UTC] [2025-09-23 11:55:40] [INFO] Modules initialized successfully
[23-Sep-2025 11:56:47 UTC] [2025-09-23 11:56:47] [INFO] Modules initialized successfully
[23-Sep-2025 11:56:54 UTC] [2025-09-23 11:56:54] [INFO] Modules initialized successfully
[23-Sep-2025 11:56:57 UTC] [2025-09-23 11:56:57] [INFO] Modules initialized successfully
[23-Sep-2025 11:58:57 UTC] [2025-09-23 11:58:57] [INFO] Modules initialized successfully
[23-Sep-2025 12:00:57 UTC] [2025-09-23 12:00:57] [INFO] Modules initialized successfully
[23-Sep-2025 12:02:58 UTC] [2025-09-23 12:02:58] [INFO] Modules initialized successfully
[23-Sep-2025 12:04:58 UTC] [2025-09-23 12:04:58] [INFO] Modules initialized successfully
[23-Sep-2025 12:06:58 UTC] [2025-09-23 12:06:58] [INFO] Modules initialized successfully
[23-Sep-2025 12:06:59 UTC] [2025-09-23 12:06:59] [INFO] Modules initialized successfully
[23-Sep-2025 12:08:33 UTC] [2025-09-23 12:08:33] [INFO] Modules initialized successfully
