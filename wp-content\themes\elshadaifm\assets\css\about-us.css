/* ==========================================================================
   ABOUT US PAGE STYLES - PROFESSIONAL LAYOUT
   ========================================================================== */

/* Container & Spacing System */
.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Professional Introduction Section */
.about-intro {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
    will-change: transform;
    transition: transform 0.1s ease-out;
}

.about-intro::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.about-intro::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(124, 58, 237, 0.06) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.about-content-wrapper {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.about-header {
    margin-bottom: 60px;
}

.about-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 24px;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
}

.about-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.4);
}

.about-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #1e293b 0%, #8B5CF6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.8;
    margin: 0 auto;
    max-width: 700px;
}

/* Professional Highlight Cards */
.about-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.highlight-card {
    background: white;
    padding: 50px 40px;
    border-radius: 24px;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 10px 40px rgba(139, 92, 246, 0.08);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.highlight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    transition: left 0.6s ease;
}

.highlight-card:hover::before {
    left: 100%;
}

.highlight-card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 25px 60px rgba(139, 92, 246, 0.15);
}

.highlight-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    transition: all 0.3s ease;
}

.highlight-card:hover .highlight-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
}

.highlight-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e293b;
}

.highlight-card p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.1rem;
}

/* Enhanced History Section */
.history-section {
    padding: 120px 0;
    background: #ffffff;
    position: relative;
}

.history-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.section-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 80px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.section-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 20px;
    color: #1e293b;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

/* Professional Timeline */
.history-timeline {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 60px 0;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, transparent 0%, #8B5CF6 20%, #7C3AED 80%, transparent 100%);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 80px;
    position: relative;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.timeline-item.visible {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-content {
    flex: 1;
    max-width: 400px;
    padding: 35px;
    background: white;
    border-radius: 24px;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 20px 40px rgba(139, 92, 246, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

/* Content positioning for alternating layout */
.timeline-item:nth-child(odd) .timeline-content {
    margin-right: 60px;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 60px;
}

/* Arrow indicators */
.timeline-content::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 12px solid transparent;
    transform: translateY(-50%);
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -24px;
    border-left-color: white;
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -24px;
    border-right-color: white;
}

.timeline-content:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 30px 60px rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.3);
}

/* Timeline connector dots */
.timeline-item::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: white;
    border: 4px solid #8B5CF6;
    border-radius: 50%;
    z-index: 15;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

.timeline-item:hover::before {
    border-color: #7C3AED;
    transform: translate(-50%, -50%) scale(1.3);
    box-shadow: 0 0 0 8px rgba(139, 92, 246, 0.2);
}

.timeline-date {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 14px 28px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 0.95rem;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
    z-index: 20;
    min-width: 140px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    white-space: nowrap;
    letter-spacing: 0.5px;
}

.timeline-date.now {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }
    50% {
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
    }
}

.timeline-date:hover {
    transform: translate(-50%, -50%) scale(1.08);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.5);
}

/* Timeline Line Animation */
@keyframes timeline-line-draw {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: 100%;
        opacity: 1;
    }
}

.history-timeline::before {
    animation: var(--line-animation, none);
}

/* Timeline Content Animations */
.timeline-item:nth-child(1) { animation-delay: 0.1s; }
.timeline-item:nth-child(2) { animation-delay: 0.3s; }
.timeline-item:nth-child(3) { animation-delay: 0.5s; }
.timeline-item:nth-child(4) { animation-delay: 0.7s; }

/* Enhanced hover effects */
.timeline-item:hover .timeline-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.timeline-item:hover .timeline-date:not(.now) {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
}

.timeline-content h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 18px;
    color: #1e293b;
    position: relative;
    padding-bottom: 8px;
}

.timeline-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #8B5CF6, #7C3AED);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.timeline-content:hover h3::after {
    width: 60px;
}

.timeline-content p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.05rem;
    text-align: justify;
}

/* Vision & Mission Section */
.vision-mission-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.vision-mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.vision-card, .mission-card {
    border-radius: 24px;
    padding: 50px;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.vision-card {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    color: white;
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.3);
}

.mission-card {
    background: white;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.vision-card::after, .mission-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vision-card:hover::after, .mission-card:hover::after {
    opacity: 1;
}

.vision-icon, .mission-icon {
    width: 80px;
    height: 80px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.vision-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.mission-icon {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
}

.vision-card:hover .vision-icon, .mission-card:hover .mission-icon {
    transform: scale(1.1) rotate(5deg);
}

.vision-card h3, .mission-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.vision-card h3 {
    color: white;
}

.mission-card h3 {
    color: #1e293b;
}

.vision-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    font-size: 1.1rem;
    margin: 0;
}

.mission-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mission-list li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(139, 92, 246, 0.05);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
}

.mission-list li:hover {
    transform: translateX(10px);
    background: rgba(139, 92, 246, 0.1);
    padding-left: 30px;
}

.mission-list li i {
    color: #8B5CF6;
    font-size: 1.25rem;
    margin-top: 2px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.mission-list li:hover i {
    color: #7C3AED;
}

.mission-list li span {
    flex: 1;
    color: #475569;
    line-height: 1.6;
}

.mission-list li strong {
    color: #1e293b;
    font-weight: 600;
}

/* Values Section */
.values-section {
    padding: 120px 0;
    background: white;
    position: relative;
}

.values-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.value-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8B5CF6, #7C3AED);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.value-card:hover::before {
    transform: scaleX(1);
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.value-card:nth-child(1) .value-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.value-card:nth-child(2) .value-icon {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
}

.value-card:nth-child(3) .value-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.value-card:nth-child(4) .value-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.value-card:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
}

.value-card h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e293b;
}

.value-card p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.1rem;
}

/* Stats Section */
.stats-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.stats-section::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-family: 'Poppins', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
    color: #fbbf24;
    transform: scale(1.1);
}

.stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.cta-content {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.cta-text {
    color: white;
}

.cta-title {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    color: white;
}

.cta-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
}

.cta-action {
    text-align: right;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 18px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.cta-button:hover::before {
    width: 300px;
    height: 300px;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(139, 92, 246, 0.5);
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-container {
        padding: 0 40px;
    }

    .vision-mission-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .cta-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .cta-action {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .about-intro, .history-section, .vision-mission-section, .values-section, .stats-section, .cta-section {
        padding: 60px 0;
    }

    .about-container {
        padding: 0 20px;
    }

    .about-title {
        font-size: 2rem;
    }

    .about-highlights {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Mobile Timeline Layout */
    .history-timeline {
        padding: 40px 0;
        max-width: 100%;
    }

    .history-timeline::before {
        left: 30px;
        width: 2px;
    }

    .timeline-item {
        flex-direction: column !important;
        align-items: flex-start;
        margin-bottom: 50px;
        padding-left: 80px;
        position: relative;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin: 0 !important;
        width: 100%;
        max-width: none;
    }

    .timeline-content::before {
        display: none;
    }

    .timeline-content {
        padding: 25px;
        border-radius: 16px;
    }

    .timeline-date {
        position: absolute;
        left: 30px;
        top: 20px;
        transform: translateX(-50%);
        min-width: auto;
        padding: 10px 18px;
        font-size: 0.85rem;
        white-space: nowrap;
    }

    .timeline-item::before {
        left: 30px;
        top: 25px;
        width: 12px;
        height: 12px;
        border-width: 3px;
    }

    .timeline-content h3 {
        font-size: 1.25rem;
        margin-bottom: 12px;
    }

    .timeline-content p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Mobile Timeline Specific Styles */
    .mobile-timeline {
        padding-left: 0;
    }

    .mobile-timeline .timeline-item {
        animation: slideInFromLeft 0.6s ease-out forwards;
        animation-fill-mode: both;
    }

    @keyframes slideInFromLeft {
        from {
            opacity: 0;
            transform: translateX(-30px) translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0) translateY(0);
        }
    }

    /* Improved mobile date positioning */
    .mobile-timeline .timeline-date {
        font-size: 0.8rem;
        padding: 8px 14px;
        min-width: 100px;
    }

    .mobile-timeline .timeline-item::before {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15);
    }

    /* Additional mobile optimizations */
    .mobile-timeline .timeline-content h3 {
        font-size: 1.3rem;
    }

    .mobile-timeline .timeline-content p {
        font-size: 1rem;
        text-align: left;
    }
}

/* Extra small devices (phones, 480px and down) */
@media (max-width: 480px) {
    .about-container {
        padding: 0 15px;
    }

    .history-timeline {
        padding: 30px 0;
    }

    .timeline-item {
        padding-left: 70px;
        margin-bottom: 40px;
    }

    .timeline-content {
        padding: 20px;
        border-radius: 12px;
    }

    .timeline-content h3 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }

    .timeline-content p {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .timeline-date {
        font-size: 0.75rem;
        padding: 6px 12px;
        min-width: 80px;
    }

    .timeline-item::before {
        width: 10px;
        height: 10px;
        border-width: 2px;
    }
}

/* Timeline Progress Indicator */
.timeline-progress {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    z-index: 100;
    display: none;
}

.timeline-progress.visible {
    display: block;
}

.timeline-progress-bar {
    width: 4px;
    height: 200px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.timeline-progress-fill {
    width: 100%;
    height: 0%;
    background: linear-gradient(180deg, #8B5CF6, #7C3AED);
    border-radius: 2px;
    transition: height 0.3s ease;
}

.timeline-progress-dots {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 0;
}

.timeline-progress-dot {
    width: 8px;
    height: 8px;
    background: rgba(139, 92, 246, 0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.timeline-progress-dot.active {
    background: #8B5CF6;
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* Hide progress indicator on mobile */
@media (max-width: 768px) {
    .timeline-progress {
        display: none !important;
    }

    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .vision-card, .mission-card {
        padding: 30px 20px;
    }

    .vision-card h3, .mission-card h3 {
        font-size: 1.5rem;
    }

    .cta-title {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced Timeline Styles */
/* Mobile expanded state */
@media (max-width: 768px) {
    .timeline-content.expanded {
        transform: scale(1.02);
        box-shadow: 0 15px 40px rgba(139, 92, 246, 0.2);
        border-color: rgba(139, 92, 246, 0.3);
    }

    .timeline-content.expanded h3::after {
        width: 80px;
    }

    /* Add tap indicator for mobile */
    .timeline-item::after {
        content: '👆 Tap untuk detail';
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.75rem;
        color: #8B5CF6;
        opacity: 0.7;
        pointer-events: none;
    }

    .timeline-item.visible::after {
        animation: fadeInUp 0.5s ease 1s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
        }
        to {
            opacity: 0.7;
            transform: translateX(-50%) translateY(0);
        }
    }
}

/* Accessibility improvements */
.timeline-item:focus-within {
    outline: 2px solid #8B5CF6;
    outline-offset: 4px;
    border-radius: 8px;
}

.timeline-date:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .timeline-progress,
    .timeline-item::before,
    .timeline-item::after,
    .history-timeline::before {
        display: none !important;
    }

    .timeline-item {
        break-inside: avoid;
        margin-bottom: 30px;
        flex-direction: column !important;
    }

    .timeline-content {
        box-shadow: none !important;
        border: 1px solid #e2e8f0 !important;
        margin: 0 !important;
        max-width: none !important;
    }

    .timeline-date {
        position: static !important;
        transform: none !important;
        display: inline-block;
        margin-bottom: 10px;
        background: #8B5CF6 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .timeline-item,
    .timeline-content,
    .timeline-date,
    .timeline-item::before,
    .timeline-progress-fill {
        transition: none !important;
        animation: none !important;
    }

    .history-timeline::before {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .timeline-content {
        border: 2px solid #000000;
        background: #ffffff;
    }

    .timeline-date {
        border: 2px solid #ffffff;
        background: #000000 !important;
        color: #ffffff !important;
    }

    .timeline-item::before {
        border-color: #000000;
        background: #ffffff;
    }

    .history-timeline::before {
        background: #000000;
    }
}

/* Page Loading States */
body:not(.page-loaded) .timeline-item {
    opacity: 0;
    transform: translateY(50px);
}

body.page-loaded .timeline-item {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth scroll enhancement */
html {
    scroll-behavior: smooth;
}

/* Timeline item focus states for keyboard navigation */
.timeline-item:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 4px;
    border-radius: 8px;
}

.timeline-item:focus .timeline-content {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.2);
}

/* Enhanced timeline date hover effects */
.timeline-date:hover {
    cursor: pointer;
}

.timeline-date.now:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

/* Loading animation for timeline line */
@keyframes timeline-line-grow {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: 100%;
        opacity: 1;
    }
}

.history-timeline.animate-line::before {
    animation: timeline-line-grow 2s ease-out 0.5s both;
}

/* Staggered animation for timeline items */
.timeline-item:nth-child(1) { transition-delay: 0.1s; }
.timeline-item:nth-child(2) { transition-delay: 0.3s; }
.timeline-item:nth-child(3) { transition-delay: 0.5s; }
.timeline-item:nth-child(4) { transition-delay: 0.7s; }
.timeline-item:nth-child(5) { transition-delay: 0.9s; }

/* Fallback for older browsers */
@supports not (display: grid) {
    .timeline-item {
        display: block;
        margin-bottom: 40px;
    }

    .timeline-content {
        margin: 0 0 20px 0 !important;
        max-width: none !important;
    }

    .timeline-date {
        position: relative !important;
        transform: none !important;
        display: inline-block;
        margin-bottom: 15px;
    }
}