/* ==========================================================================
   ABOUT US PAGE STYLES - MODULAR ARCHITECTURE
   ========================================================================== */

/*
 * This file imports all modular CSS components for the About Us page.
 * Each component is separated into its own file for better maintainability.
 *
 * File Structure:
 * ├── about-base.css          - Base styles, containers, animations
 * ├── about-intro.css         - Introduction section with highlights
 * ├── about-timeline.css      - Timeline sejarah with progress indicator
 * ├── about-vision-mission.css - Vision & Mission cards
 * ├── about-values.css        - Values section with icons
 * ├── about-stats.css         - Statistics section
 * ├── about-cta.css          - Call-to-action section
 * └── about-responsive.css    - All responsive styles & accessibility


### 1. **about-base.css**
- Container & spacing system
- Section headers styling
- Animation classes (fade-in, slide-in, scale-in)
- Page loading states
- Basic accessibility styles

### 2. **about-intro.css**
- Hero introduction section
- About badge, title, subtitle
- Highlight cards with hover effects
- Background gradients and decorative elements

### 3. **about-timeline.css** ⭐ *Most Complex*
- Timeline sejarah layout
- Timeline items with alternating design
- Timeline dates and connector dots
- Progress indicator (desktop only)
- Timeline animations and keyframes
- Staggered animation delays

### 4. **about-vision-mission.css**
- Vision & Mission grid layout
- Vision card (purple gradient)
- Mission card (white with list)
- Mission list with hover effects

### 5. **about-values.css**
- Values grid layout
- Value cards with colored icons
- Hover effects and animations
- Icon color variations per card

### 6. **about-stats.css**
- Statistics section with gradient background
- Stat items with counter animations
- Decorative background elements

### 7. **about-cta.css**
- Call-to-action section
- CTA button with ripple effect
- Grid layout for content and action

### 8. **about-responsive.css** ⭐ *Comprehensive*
- Tablet styles (1024px and below)
- Mobile styles (768px and below)
- Extra small devices (480px and below)
- Mobile timeline specific styles
- Print styles
- Accessibility (reduced motion, high contrast)
- Fallbacks for older browsers
 */

/* Import all modular CSS files */
@import url('about-us/about-base.css');
@import url('about-us/about-intro.css');
@import url('about-us/about-timeline.css');
@import url('about-us/about-vision-mission.css');
@import url('about-us/about-values.css');
@import url('about-us/about-stats.css');
@import url('about-us/about-cta.css');
@import url('about-us/about-responsive.css');

