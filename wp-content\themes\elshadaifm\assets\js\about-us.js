/* ==========================================================================
   ABOUT US PAGE JAVASCRIPT - INTERACTIONS & ANIMATIONS
   ========================================================================== */

document.addEventListener('DOMContentLoaded', function() {

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');

                // Trigger counter animation for stats
                if (entry.target.classList.contains('stat-item')) {
                    animateStatNumber(entry.target);
                }
            }
        });
    }, observerOptions);

    // Observe all animated elements
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .highlight-card, .value-card, .timeline-item, .stat-item');
    animatedElements.forEach(el => observer.observe(el));

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Parallax effect for hero section - Fixed direction with bounds
    const heroSection = document.querySelector('.about-intro');
    if (heroSection) {
        const heroRect = heroSection.getBoundingClientRect();
        const heroHeight = heroSection.offsetHeight;

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroOffset = heroSection.offsetTop;

            // Only apply parallax when scrolling within hero section
            if (scrolled <= heroOffset + heroHeight) {
                const parallax = scrolled * 0.2; // Further reduced for subtlety
                heroSection.style.transform = `translateY(-${parallax}px)`; // Negative for proper direction
            }
        });
    }

    // Timeline animation on scroll
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(50px)';

        setTimeout(() => {
            item.style.transition = 'all 0.6s ease';
        }, index * 100);
    });

    const timelineObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.3 });

    timelineItems.forEach(item => timelineObserver.observe(item));

    // Counter animation for statistics
    function animateStatNumber(statItem) {
        const numberElement = statItem.querySelector('.stat-number');
        if (!numberElement || numberElement.dataset.animated) return;

        numberElement.dataset.animated = 'true';
        const finalText = numberElement.textContent;
        let currentNumber = 0;

        // Extract numeric value
        const numericValue = parseInt(finalText.replace(/\D/g, ''));
        const increment = numericValue / 50;
        const duration = 2000; // 2 seconds
        const stepTime = duration / 50;

        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= numericValue) {
                currentNumber = numericValue;
                clearInterval(timer);
            }

            // Format number with original text
            if (finalText.includes('+')) {
                numberElement.textContent = Math.floor(currentNumber) + '+';
            } else if (finalText.includes('/')) {
                numberElement.textContent = Math.floor(currentNumber) + '/';
            } else {
                numberElement.textContent = Math.floor(currentNumber);
            }
        }, stepTime);
    }

    // Mouse move effect for cards
    document.querySelectorAll('.highlight-card, .value-card').forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });

    // Add ripple effect to CTA button
    const ctaButton = document.querySelector('.cta-button');
    if (ctaButton) {
        ctaButton.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // Add loading animation classes
    document.querySelectorAll('.highlight-card, .value-card, .vision-card, .mission-card').forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.transitionDelay = `${index * 0.1}s`;
    });

    // Mobile menu handling (if needed)
    function handleMobileMenu() {
        if (window.innerWidth <= 768) {
            // Adjust timeline for mobile
            const timeline = document.querySelector('.history-timeline');
            if (timeline) {
                timeline.style.paddingLeft = '40px';
            }
        }
    }

    // Initialize mobile handling
    handleMobileMenu();
    window.addEventListener('resize', handleMobileMenu);

    // Add smooth reveal animation for sections
    const sections = document.querySelectorAll('.about-intro, .history-section, .vision-mission-section, .values-section, .stats-section, .cta-section');

    const sectionObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });

    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(50px)';
        section.style.transition = 'all 0.8s ease';
        sectionObserver.observe(section);
    });

    // Initialize tooltips for value cards
    const valueCards = document.querySelectorAll('.value-card');
    valueCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(360deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // Skip to content with Alt + S
        if (e.altKey && e.key === 's') {
            const mainContent = document.querySelector('#main');
            if (mainContent) {
                mainContent.focus();
                mainContent.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Navigate sections with arrow keys
        if (e.key === 'ArrowDown' && e.ctrlKey) {
            const currentSection = document.querySelector('section:focus') || document.querySelector('section');
            if (currentSection) {
                const nextSection = currentSection.nextElementSibling;
                if (nextSection && nextSection.tagName === 'SECTION') {
                    nextSection.focus();
                    nextSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }
    });

    // Performance optimization: throttle scroll events
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            window.cancelAnimationFrame(scrollTimeout);
        }

        scrollTimeout = window.requestAnimationFrame(function() {
            // Handle scroll-based animations here
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;

            // Parallax effect is handled above, no need for duplication
        });
    });

    // Initialize AOS (Animate On Scroll) functionality
    function initAOS() {
        const aosElements = document.querySelectorAll('[data-aos]');

        const aosObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const animation = entry.target.dataset.aos || 'fade-up';
                    const delay = entry.target.dataset.aosDelay || 0;

                    setTimeout(() => {
                        entry.target.classList.add('aos-animate');
                    }, delay);

                    aosObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        aosElements.forEach(element => {
            aosObserver.observe(element);
        });
    }

    // Initialize AOS
    initAOS();

    // Console log for debugging (remove in production)
    console.log('About Us page initialized successfully');
});