/* ==========================================================================
   ABOUT US PAGE JAVASCRIPT - INTERACTIONS & ANIMATIONS
   ========================================================================== */

document.addEventListener('DOMContentLoaded', function() {

    // Add 'js' class to body to enable JavaScript-dependent styles
    document.body.classList.add('js');

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');

                // Trigger counter animation for stats
                if (entry.target.classList.contains('stat-item')) {
                    animateStatNumber(entry.target);
                }
            }
        });
    }, observerOptions);

    // Observe all animated elements
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .highlight-card, .value-card, .timeline-item, .stat-item');
    animatedElements.forEach(el => observer.observe(el));

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Parallax effect for hero section - Fixed direction with bounds
    const heroSection = document.querySelector('.about-intro');
    if (heroSection) {
        const heroRect = heroSection.getBoundingClientRect();
        const heroHeight = heroSection.offsetHeight;

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroOffset = heroSection.offsetTop;

            // Only apply parallax when scrolling within hero section
            if (scrolled <= heroOffset + heroHeight) {
                const parallax = scrolled * 0.2; // Further reduced for subtlety
                heroSection.style.transform = `translateY(-${parallax}px)`; // Negative for proper direction
            }
        });
    }

    // Enhanced Timeline animation on scroll
    const timelineItems = document.querySelectorAll('.timeline-item');
    console.log('Timeline items found:', timelineItems.length);

    // Initialize timeline items
    timelineItems.forEach((item, index) => {
        // Set initial state only if CSS animation classes are supported
        if (window.IntersectionObserver) {
            item.style.opacity = '0';
            item.style.transform = 'translateY(60px)';
            item.style.transitionDelay = `${index * 0.15}s`;
        } else {
            // Fallback: show items immediately if no Intersection Observer
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
            item.classList.add('visible');
        }
    });

    // Create timeline observer with better threshold
    const timelineObserver = new IntersectionObserver(function(entries) {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add visible class for CSS animation
                setTimeout(() => {
                    entry.target.classList.add('visible');
                }, index * 100);

                // Unobserve after animation to prevent re-triggering
                timelineObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe all timeline items
    timelineItems.forEach(item => timelineObserver.observe(item));

    // Fallback: Show timeline items after 3 seconds if they're still hidden
    setTimeout(() => {
        timelineItems.forEach((item, index) => {
            if (item.style.opacity === '0' || !item.classList.contains('visible')) {
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                    item.classList.add('visible');
                }, index * 200);
            }
        });
    }, 3000);

    // Add timeline line animation
    const timelineLine = document.querySelector('.history-timeline::before');
    const timelineSection = document.querySelector('.history-section');

    if (timelineSection) {
        const timelineSectionObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate timeline line
                    const timeline = entry.target.querySelector('.history-timeline');
                    if (timeline) {
                        timeline.style.setProperty('--line-animation', 'timeline-line-draw 2s ease-out forwards');
                    }
                }
            });
        }, { threshold: 0.1 });

        timelineSectionObserver.observe(timelineSection);
    }

    // Counter animation for statistics
    function animateStatNumber(statItem) {
        const numberElement = statItem.querySelector('.stat-number');
        if (!numberElement || numberElement.dataset.animated) return;

        numberElement.dataset.animated = 'true';
        const finalText = numberElement.textContent;
        let currentNumber = 0;

        // Extract numeric value
        const numericValue = parseInt(finalText.replace(/\D/g, ''));
        const increment = numericValue / 50;
        const duration = 2000; // 2 seconds
        const stepTime = duration / 50;

        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= numericValue) {
                currentNumber = numericValue;
                clearInterval(timer);
            }

            // Format number with original text
            if (finalText.includes('+')) {
                numberElement.textContent = Math.floor(currentNumber) + '+';
            } else if (finalText.includes('/')) {
                numberElement.textContent = Math.floor(currentNumber) + '/';
            } else {
                numberElement.textContent = Math.floor(currentNumber);
            }
        }, stepTime);
    }

    // Mouse move effect for cards
    document.querySelectorAll('.highlight-card, .value-card').forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });

    // Add ripple effect to CTA button
    const ctaButton = document.querySelector('.cta-button');
    if (ctaButton) {
        ctaButton.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // Add loading animation classes
    document.querySelectorAll('.highlight-card, .value-card, .vision-card, .mission-card').forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.transitionDelay = `${index * 0.1}s`;
    });

    // Enhanced Mobile Timeline Handling
    function handleMobileTimeline() {
        const isMobile = window.innerWidth <= 768;
        const timeline = document.querySelector('.history-timeline');
        const timelineItems = document.querySelectorAll('.timeline-item');

        if (timeline && timelineItems.length > 0) {
            if (isMobile) {
                // Mobile-specific adjustments
                timeline.classList.add('mobile-timeline');

                // Adjust timeline items for mobile
                timelineItems.forEach((item, index) => {
                    item.style.animationDelay = `${index * 0.2}s`;

                    // Reset any desktop transforms
                    item.style.transform = 'translateY(30px)';
                    item.style.opacity = '0';
                });

                // Re-observe items for mobile animation
                const mobileObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                            entry.target.classList.add('visible');
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -20px 0px'
                });

                timelineItems.forEach(item => mobileObserver.observe(item));

            } else {
                // Desktop handling
                timeline.classList.remove('mobile-timeline');
            }
        }
    }

    // Initialize mobile handling
    handleMobileTimeline();

    // Debounced resize handler
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleMobileTimeline, 250);
    });

    // Add smooth reveal animation for sections
    const sections = document.querySelectorAll('.about-intro, .history-section, .vision-mission-section, .values-section, .stats-section, .cta-section');

    const sectionObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });

    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(50px)';
        section.style.transition = 'all 0.8s ease';
        sectionObserver.observe(section);
    });

    // Initialize tooltips for value cards
    const valueCards = document.querySelectorAll('.value-card');
    valueCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(360deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // Skip to content with Alt + S
        if (e.altKey && e.key === 's') {
            const mainContent = document.querySelector('#main');
            if (mainContent) {
                mainContent.focus();
                mainContent.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Navigate sections with arrow keys
        if (e.key === 'ArrowDown' && e.ctrlKey) {
            const currentSection = document.querySelector('section:focus') || document.querySelector('section');
            if (currentSection) {
                const nextSection = currentSection.nextElementSibling;
                if (nextSection && nextSection.tagName === 'SECTION') {
                    nextSection.focus();
                    nextSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }
    });

    // Performance optimization: throttle scroll events
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            window.cancelAnimationFrame(scrollTimeout);
        }

        scrollTimeout = window.requestAnimationFrame(function() {
            // Handle scroll-based animations here
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;

            // Parallax effect is handled above, no need for duplication
        });
    });

    // Initialize AOS (Animate On Scroll) functionality
    function initAOS() {
        const aosElements = document.querySelectorAll('[data-aos]');

        const aosObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const animation = entry.target.dataset.aos || 'fade-up';
                    const delay = entry.target.dataset.aosDelay || 0;

                    setTimeout(() => {
                        entry.target.classList.add('aos-animate');
                    }, delay);

                    aosObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        aosElements.forEach(element => {
            aosObserver.observe(element);
        });
    }

    // Initialize AOS
    initAOS();

    // Timeline Progress Indicator
    function createTimelineProgress() {
        const historySection = document.querySelector('.history-section');
        const timelineItems = document.querySelectorAll('.timeline-item');

        if (!historySection || timelineItems.length === 0 || window.innerWidth <= 768) {
            return;
        }

        // Create progress indicator HTML
        const progressHTML = `
            <div class="timeline-progress" id="timelineProgress">
                <div class="timeline-progress-bar">
                    <div class="timeline-progress-fill"></div>
                    <div class="timeline-progress-dots">
                        ${Array.from(timelineItems).map((_, index) =>
                            `<div class="timeline-progress-dot" data-timeline-index="${index}"></div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;

        // Insert progress indicator
        document.body.insertAdjacentHTML('beforeend', progressHTML);

        const progressIndicator = document.getElementById('timelineProgress');
        const progressFill = progressIndicator.querySelector('.timeline-progress-fill');
        const progressDots = progressIndicator.querySelectorAll('.timeline-progress-dot');

        // Show/hide progress indicator based on history section visibility
        const progressObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    progressIndicator.classList.add('visible');
                } else {
                    progressIndicator.classList.remove('visible');
                }
            });
        }, { threshold: 0.1 });

        progressObserver.observe(historySection);

        // Update progress based on scroll
        function updateTimelineProgress() {
            const sectionRect = historySection.getBoundingClientRect();
            const sectionTop = sectionRect.top + window.pageYOffset;
            const sectionHeight = sectionRect.height;
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;

            // Calculate progress percentage
            const sectionStart = sectionTop - windowHeight / 2;
            const sectionEnd = sectionTop + sectionHeight - windowHeight / 2;
            const progress = Math.max(0, Math.min(1, (scrolled - sectionStart) / (sectionEnd - sectionStart)));

            // Update progress fill
            progressFill.style.height = `${progress * 100}%`;

            // Update active dots
            const activeIndex = Math.floor(progress * timelineItems.length);
            progressDots.forEach((dot, index) => {
                if (index <= activeIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Add click handlers to progress dots
        progressDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                const targetItem = timelineItems[index];
                if (targetItem) {
                    targetItem.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            });
        });

        // Throttled scroll handler for progress
        let progressTimeout;
        window.addEventListener('scroll', () => {
            if (progressTimeout) {
                cancelAnimationFrame(progressTimeout);
            }
            progressTimeout = requestAnimationFrame(updateTimelineProgress);
        });
    }

    // Initialize timeline progress (only on desktop)
    if (window.innerWidth > 768) {
        createTimelineProgress();
    }

    // Enhanced timeline item interactions
    // timelineItems already declared above, reuse it
    timelineItems.forEach((item, index) => {
        // Add data attributes for easier targeting
        item.setAttribute('data-timeline-index', index);

        // Enhanced hover effects
        item.addEventListener('mouseenter', function() {
            this.style.zIndex = '10';

            // Subtle scale effect for content
            const content = this.querySelector('.timeline-content');
            if (content) {
                content.style.transform = 'translateY(-8px) scale(1.02)';
            }
        });

        item.addEventListener('mouseleave', function() {
            this.style.zIndex = '1';

            const content = this.querySelector('.timeline-content');
            if (content) {
                content.style.transform = 'translateY(0) scale(1)';
            }
        });

        // Add click handler for mobile
        if (window.innerWidth <= 768) {
            item.addEventListener('click', function() {
                const content = this.querySelector('.timeline-content');
                if (content) {
                    content.classList.toggle('expanded');
                }
            });
        }
    });

    // Add loading state management
    function initializePageLoading() {
        // Add loading class to timeline items initially
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(50px)';
            item.style.transitionDelay = `${index * 0.1}s`;
        });

        // Remove loading state after page is fully loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.add('page-loaded');
            }, 500);
        });
    }

    // Initialize loading state
    initializePageLoading();

    // Add error handling for timeline animations
    function handleTimelineErrors() {
        try {
            // Check if timeline elements exist
            const timeline = document.querySelector('.history-timeline');
            const timelineItems = document.querySelectorAll('.timeline-item');

            if (!timeline || timelineItems.length === 0) {
                console.warn('Timeline elements not found');
                return;
            }

            // Fallback for browsers that don't support Intersection Observer
            if (!window.IntersectionObserver) {
                timelineItems.forEach(item => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                    item.classList.add('visible');
                });
            }
        } catch (error) {
            console.error('Timeline initialization error:', error);
        }
    }

    // Initialize error handling
    handleTimelineErrors();

    // Performance optimization: Use passive event listeners
    const passiveSupported = (() => {
        let passive = false;
        try {
            const options = {
                get passive() {
                    passive = true;
                    return false;
                }
            };
            window.addEventListener('test', null, options);
            window.removeEventListener('test', null, options);
        } catch (err) {
            passive = false;
        }
        return passive;
    })();

    // Add smooth scroll behavior for better UX
    if ('scrollBehavior' in document.documentElement.style) {
        document.documentElement.style.scrollBehavior = 'smooth';
    }

    // Console log for debugging (remove in production)
    console.log('About Us page with enhanced timeline initialized successfully');
    console.log('Timeline items found:', document.querySelectorAll('.timeline-item').length);
    console.log('Mobile view:', window.innerWidth <= 768);
});