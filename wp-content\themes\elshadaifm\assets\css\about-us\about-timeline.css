/* ==========================================================================
   ABOUT US - TIMELINE SECTION
   ========================================================================== */

/* Enhanced History Section */
.history-section {
    padding: 120px 0;
    background: #ffffff;
    position: relative;
}

.history-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* Professional Timeline */
.history-timeline {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    padding: 60px 0;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, transparent 0%, #8B5CF6 20%, #7C3AED 80%, transparent 100%);
    transform: translateX(-50%);
    border-radius: 2px;
    animation: var(--line-animation, none);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 80px;
    position: relative;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.timeline-item.visible {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-content {
    flex: 1;
    max-width: 400px;
    padding: 35px;
    background: white;
    border-radius: 24px;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 20px 40px rgba(139, 92, 246, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

/* Content positioning for alternating layout */
.timeline-item:nth-child(odd) .timeline-content {
    margin-right: 60px;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 60px;
}

/* Arrow indicators */
.timeline-content::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 12px solid transparent;
    transform: translateY(-50%);
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -24px;
    border-left-color: white;
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -24px;
    border-right-color: white;
}

.timeline-content:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 30px 60px rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Timeline connector dots */
.timeline-item::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: white;
    border: 4px solid #8B5CF6;
    border-radius: 50%;
    z-index: 15;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

.timeline-item:hover::before {
    border-color: #7C3AED;
    transform: translate(-50%, -50%) scale(1.3);
    box-shadow: 0 0 0 8px rgba(139, 92, 246, 0.2);
}

.timeline-item:hover .timeline-date {
    transform: translate(-50%, -50%) scale(1.1);
}

.timeline-date {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 14px 28px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 0.95rem;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
    z-index: 20;
    min-width: 140px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    white-space: nowrap;
    letter-spacing: 0.5px;
}

.timeline-date.now {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    animation: pulse 2s infinite;
}

.timeline-date:hover {
    transform: translate(-50%, -50%) scale(1.08);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.5);
}

.timeline-date:not(.now):hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
}

.timeline-content h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 18px;
    color: #1e293b;
    position: relative;
    padding-bottom: 8px;
}

.timeline-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #8B5CF6, #7C3AED);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.timeline-content:hover h3::after {
    width: 60px;
}

.timeline-content p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.05rem;
    text-align: justify;
}

/* Timeline Animations */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }
    50% {
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
    }
}

@keyframes timeline-line-draw {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: 100%;
        opacity: 1;
    }
}

@keyframes timeline-line-grow {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: 100%;
        opacity: 1;
    }
}

.history-timeline.animate-line::before {
    animation: timeline-line-grow 2s ease-out 0.5s both;
}

/* Staggered animation delays */
.timeline-item:nth-child(1) { animation-delay: 0.1s; transition-delay: 0.1s; }
.timeline-item:nth-child(2) { animation-delay: 0.3s; transition-delay: 0.3s; }
.timeline-item:nth-child(3) { animation-delay: 0.5s; transition-delay: 0.5s; }
.timeline-item:nth-child(4) { animation-delay: 0.7s; transition-delay: 0.7s; }
.timeline-item:nth-child(5) { animation-delay: 0.9s; transition-delay: 0.9s; }

/* Timeline Progress Indicator */
.timeline-progress {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    z-index: 100;
    display: none;
}

.timeline-progress.visible {
    display: block;
}

.timeline-progress-bar {
    width: 4px;
    height: 200px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.timeline-progress-fill {
    width: 100%;
    height: 0%;
    background: linear-gradient(180deg, #8B5CF6, #7C3AED);
    border-radius: 2px;
    transition: height 0.3s ease;
}

.timeline-progress-dots {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 0;
}

.timeline-progress-dot {
    width: 8px;
    height: 8px;
    background: rgba(139, 92, 246, 0.3);
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.timeline-progress-dot.active {
    background: #8B5CF6;
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}
