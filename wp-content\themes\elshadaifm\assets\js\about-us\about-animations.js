/* ==========================================================================
   ABOUT US - ANIMATIONS & OBSERVERS
   ========================================================================== */

/**
 * About Us Animations Module
 * Handles scroll animations, intersection observers, and visual effects
 */
window.AboutUsAnimations = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;
    let observers = [];

    // Public API
    const api = {
        init: init,
        destroy: destroy,
        animateStatNumber: animateStatNumber,
        initParallax: initParallax
    };

    /**
     * Initialize animations
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsAnimations: Initializing...');

        // Setup intersection observers
        setupIntersectionObservers();

        // Setup parallax effects
        initParallax();

        // Setup section animations
        setupSectionAnimations();

        // Setup loading animations
        setupLoadingAnimations();

        isInitialized = true;
        console.log('AboutUsAnimations: Initialized successfully');
    }

    /**
     * Destroy all observers and animations
     */
    function destroy() {
        observers.forEach(observer => observer.disconnect());
        observers = [];
        isInitialized = false;
    }

    /**
     * Setup intersection observers for scroll animations
     */
    function setupIntersectionObservers() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');

                    // Trigger counter animation for stats
                    if (entry.target.classList.contains('stat-item')) {
                        animateStatNumber(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe all animated elements
        const animatedElements = document.querySelectorAll(
            '.fade-in, .slide-in-left, .slide-in-right, .scale-in, ' +
            '.highlight-card, .value-card, .timeline-item, .stat-item'
        );
        
        animatedElements.forEach(el => observer.observe(el));
        observers.push(observer);
    }

    /**
     * Setup parallax effects
     */
    function initParallax() {
        const heroSection = document.querySelector('.about-intro');
        if (!heroSection) return;

        const heroHeight = heroSection.offsetHeight;

        const parallaxHandler = AboutUsBase.utils.throttle(() => {
            const scrolled = window.pageYOffset;
            const heroOffset = heroSection.offsetTop;

            // Only apply parallax when scrolling within hero section
            if (scrolled <= heroOffset + heroHeight) {
                const parallax = scrolled * 0.2;
                heroSection.style.transform = `translateY(-${parallax}px)`;
            }
        }, 16); // ~60fps

        window.addEventListener('scroll', parallaxHandler);
    }

    /**
     * Setup section reveal animations
     */
    function setupSectionAnimations() {
        const sections = document.querySelectorAll(
            '.about-intro, .history-section, .vision-mission-section, ' +
            '.values-section, .stats-section, .cta-section'
        );

        const sectionObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        sections.forEach(section => {
            // Only hide sections if Intersection Observer is supported
            if (window.IntersectionObserver) {
                section.style.opacity = '0';
                section.style.transform = 'translateY(50px)';
                section.style.transition = 'all 0.8s ease';
                sectionObserver.observe(section);
            } else {
                // Fallback: show sections immediately
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }
        });

        observers.push(sectionObserver);

        // Fallback: Show sections after 3 seconds if still hidden
        setTimeout(() => {
            sections.forEach(section => {
                if (section.style.opacity === '0') {
                    console.warn('Section still hidden, applying fallback:', section.className);
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }
            });
        }, 3000);
    }

    /**
     * Setup loading animations
     */
    function setupLoadingAnimations() {
        // Add loading animation classes
        document.querySelectorAll('.highlight-card, .value-card, .vision-card, .mission-card')
            .forEach((card, index) => {
                card.classList.add('fade-in');
                card.style.transitionDelay = `${index * 0.1}s`;
            });

        // Remove loading state after page is fully loaded
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.add('page-loaded');
            }, 500);
        });
    }

    /**
     * Animate stat numbers with counter effect
     */
    function animateStatNumber(statItem) {
        const numberElement = statItem.querySelector('.stat-number');
        if (!numberElement || numberElement.dataset.animated) return;

        numberElement.dataset.animated = 'true';
        const finalText = numberElement.textContent;
        let currentNumber = 0;

        // Extract numeric value
        const numericValue = parseInt(finalText.replace(/\D/g, ''));
        if (isNaN(numericValue)) return;

        const increment = numericValue / 50;
        const duration = 2000; // 2 seconds
        const stepTime = duration / 50;

        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= numericValue) {
                currentNumber = numericValue;
                clearInterval(timer);
            }

            // Format number with original text
            if (finalText.includes('+')) {
                numberElement.textContent = Math.floor(currentNumber) + '+';
            } else if (finalText.includes('/')) {
                numberElement.textContent = Math.floor(currentNumber) + '/';
            } else {
                numberElement.textContent = Math.floor(currentNumber);
            }
        }, stepTime);
    }

    /**
     * Initialize AOS (Animate On Scroll) functionality
     */
    function initAOS() {
        const aosElements = document.querySelectorAll('[data-aos]');
        if (aosElements.length === 0) return;

        const aosObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.aosDelay || 0;

                    setTimeout(() => {
                        entry.target.classList.add('aos-animate');
                    }, delay);

                    aosObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        aosElements.forEach(element => {
            aosObserver.observe(element);
        });

        observers.push(aosObserver);
    }

    // Initialize AOS on module load
    setTimeout(initAOS, 100);

    return api;
})();
